/**
 * Shared CSP Utilities for SahAI CEP Extension V2
 * Common functions for Content Security Policy hash generation
 */

const crypto = require('crypto');

/**
 * Generate SHA-256 hash for script content
 */
function generateScriptHash(scriptContent) {
  const hash = crypto.createHash('sha256');
  hash.update(scriptContent, 'utf8');
  return 'sha256-' + hash.digest('base64');
}

/**
 * Extract inline scripts from HTML content
 * @param {string} htmlContent - The HTML content to parse
 * @returns {Array} Array of script objects with content and metadata
 */
function extractInlineScripts(htmlContent) {
  const scripts = [];
  const scriptRegex = /<script(?![^>]*src=)([^>]*)>([\s\S]*?)<\/script>/gi;
  let match;

  while ((match = scriptRegex.exec(htmlContent)) !== null) {
    const attributes = match[1];
    const content = match[2].trim();
    
    // Skip empty scripts or scripts with src attribute
    if (content && !attributes.includes('src=')) {
      scripts.push({
        fullMatch: match[0],
        attributes: attributes,
        content: content,
        startIndex: match.index,
        endIndex: match.index + match[0].length,
        hash: generateScriptHash(content)
      });
    }
  }

  return scripts;
}

/**
 * Update CSP with script hashes
 * @param {string} htmlContent - The HTML content containing CSP meta tag
 * @param {Array<string>} scriptHashes - Array of script hashes to add
 * @returns {string} Updated HTML content with new CSP
 */
function updateCSPWithHashes(htmlContent, scriptHashes) {
  // Find the existing CSP meta tag
  const cspRegex = /<meta\s+http-equiv=["']Content-Security-Policy["']\s+content=["']([\s\S]*?)["']\s*>/i;
  const match = cspRegex.exec(htmlContent);
  
  if (!match) {
    console.warn('⚠️  Content-Security-Policy meta tag not found');
    return htmlContent;
  }

  const originalCSP = match[1];
  
  // Parse CSP directives
  const directives = originalCSP
    .split(';')
    .map(directive => directive.trim())
    .filter(directive => directive.length > 0);

  // Find and update script-src directive
  let scriptSrcIndex = -1;
  
  for (let i = 0; i < directives.length; i++) {
    if (directives[i].startsWith('script-src')) {
      scriptSrcIndex = i;
      break;
    }
  }

  if (scriptSrcIndex === -1) {
    console.warn('⚠️  script-src directive not found in CSP');
    return htmlContent;
  }

  // Remove 'unsafe-inline' and add hashes
  let newScriptSrc = directives[scriptSrcIndex]
    .replace(/'unsafe-inline'/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  // Add script hashes
  if (scriptHashes.length > 0) {
    const hashStrings = scriptHashes.map(hash => `'${hash}'`);
    newScriptSrc += ' ' + hashStrings.join(' ');
  }

  // Clean up extra spaces
  newScriptSrc = newScriptSrc.replace(/\s+/g, ' ').trim();

  // Update the directive
  directives[scriptSrcIndex] = newScriptSrc;

  // Reconstruct CSP
  const newCSP = directives.join(';\n        ') + ';';

  // Replace in HTML
  const newCspTag = match[0].replace(originalCSP, newCSP);
  return htmlContent.replace(match[0], newCspTag);
}

module.exports = {
  generateScriptHash,
  extractInlineScripts,
  updateCSPWithHashes
};
