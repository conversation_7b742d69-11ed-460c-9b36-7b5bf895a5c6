/**
 * Vite Plugin for CSP Hash Generation
 * Automatically generates CSP hashes for inline scripts during build
 */

const { extractInlineScripts, updateCSPWithHashes } = require('./csp-utils');

/**
 * Vite Plugin for CSP Hash Generation
 */
function cspHashPlugin(options = {}) {
  const {
    enabled = true,
    logLevel = 'info'
  } = options;

  return {
    name: 'csp-hash',
    apply: 'build', // Only run during build
    
    transformIndexHtml: {
      order: 'post', // Run after other transformations
      handler(html, context) {
        if (!enabled) {
          return html;
        }

        try {
          // Extract inline scripts
          const inlineScripts = extractInlineScripts(html);
          
          if (inlineScripts.length === 0) {
            if (logLevel === 'info') {
              console.log('ℹ️  No inline scripts found for CSP hash generation');
            }
            return html;
          }

          // Generate hashes
          const scriptHashes = inlineScripts.map(script => script.hash);
          
          if (logLevel === 'info') {
            console.log(`🔐 Generated CSP hashes for ${inlineScripts.length} inline script(s):`);
            scriptHashes.forEach((hash, index) => {
              console.log(`   ${index + 1}. ${hash}`);
            });
          }

          // Update CSP
          const updatedHtml = updateCSPWithHashes(html, scriptHashes);
          
          if (logLevel === 'info') {
            console.log('✅ CSP updated with script hashes, removed \'unsafe-inline\'');
          }

          return updatedHtml;

        } catch (error) {
          console.error('❌ CSP hash generation failed:', error.message);
          
          // Return original HTML on error to prevent build failure
          return html;
        }
      }
    }
  };
}

module.exports = cspHashPlugin;
