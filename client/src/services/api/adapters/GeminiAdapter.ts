/**
 * Google Gemini Provider Adapter for SahAI V2
 * Implements Gemini API integration
 */

import { 
  ProviderAdapter, 
  AuthConfig, 
  RawModelInfo, 
  ModelInfo, 
  HealthStatus 
} from '../../../types/api';

// Static model list for Gemini (following <PERSON><PERSON>'s pattern)
const GEMINI_MODELS: Record<string, ModelInfo> = {
  'gemini-1.5-pro': {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    description: 'Google\'s most capable multimodal model',
    contextLength: 2000000,
    inputCost: 1.25,
    outputCost: 5.00,
    capabilities: ['text', 'vision', 'tools', 'long-context'],
    provider: 'gemini',
    isAvailable: true,
    lastUpdated: new Date()
  },
  'gemini-1.5-flash': {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    description: 'Fast and efficient multimodal model',
    contextLength: 1000000,
    inputCost: 0.075,
    outputCost: 0.30,
    capabilities: ['text', 'vision', 'tools'],
    provider: 'gemini',
    isAvailable: true,
    lastUpdated: new Date()
  },
  'gemini-2.0-flash-exp': {
    id: 'gemini-2.0-flash-exp',
    name: 'Gemini 2.0 Flash (Experimental)',
    description: 'Latest experimental Gemini model',
    contextLength: 1000000,
    inputCost: 0.075,
    outputCost: 0.30,
    capabilities: ['text', 'vision', 'tools', 'multimodal'],
    provider: 'gemini',
    isAvailable: true,
    lastUpdated: new Date()
  }
};

export class GeminiAdapter implements ProviderAdapter {
  readonly providerId = 'gemini';
  readonly name = 'Google Gemini';
  readonly baseUrl = 'https://generativelanguage.googleapis.com/v1';
  readonly authType = 'api-key' as const;

  async authenticate(config: AuthConfig): Promise<boolean> {
    if (!config.apiKey) return false;
    
    try {
      const response = await fetch(`${this.baseUrl}/models?key=${config.apiKey}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      return response.ok;
    } catch (error) {
      console.error('Gemini authentication failed:', error);
      return false;
    }
  }

  async discoverModels(_config: AuthConfig): Promise<RawModelInfo[]> {
    // Return static list for now
    return Object.keys(GEMINI_MODELS).map(id => ({
      id,
      name: GEMINI_MODELS[id]?.name,
      object: 'model',
      created: Date.now(),
      owned_by: 'google',
    }));
  }

  async checkHealth(config?: AuthConfig): Promise<HealthStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/models${config?.apiKey ? `?key=${config.apiKey}` : ''}`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });

      return {
        status: response.ok ? 'healthy' : 'down',
        latency: 0,
        lastCheck: new Date(),
        errorRate: 0,
        uptime: 100,
      };
    } catch (error) {
      return {
        status: 'down',
        latency: 0,
        lastCheck: new Date(),
        errorRate: 100,
        uptime: 0,
        details: {
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  normalizeModel(raw: RawModelInfo): ModelInfo {
    const staticModel = GEMINI_MODELS[raw.id];
    if (staticModel) {
      return staticModel;
    }

    // Fallback for unknown models
    return {
      id: raw.id,
      name: raw.name || raw.id,
      description: `Google Gemini ${raw.name || raw.id}`,
      contextLength: 1000000,
      inputCost: 0.075,
      outputCost: 0.30,
      capabilities: ['text', 'vision'],
      provider: this.providerId,
      isAvailable: true,
      lastUpdated: new Date(),
    };
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    if (!apiKey || apiKey.length < 20) return false;
    
    try {
      return await this.authenticate({ type: 'api-key', apiKey });
    } catch {
      return false;
    }
  }
}
