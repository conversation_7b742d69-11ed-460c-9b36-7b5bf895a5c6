/**
 * DeepSeek Provider Adapter for SahAI V2
 * Implements DeepSeek API integration
 */

import { 
  ProviderAdapter, 
  AuthConfig, 
  RawModelInfo, 
  ModelInfo, 
  HealthStatus 
} from '../../../types/api';

// Static model list for DeepSeek (following <PERSON><PERSON>'s pattern)
const DEEPSEEK_MODELS: Record<string, ModelInfo> = {
  'deepseek-chat': {
    id: 'deepseek-chat',
    name: 'DeepSeek Chat',
    description: 'DeepSeek\'s general-purpose chat model',
    contextLength: 32768,
    inputCost: 0.14,
    outputCost: 0.28,
    capabilities: ['text', 'reasoning', 'code'],
    provider: 'deepseek',
    isAvailable: true,
    lastUpdated: new Date()
  },
  'deepseek-coder': {
    id: 'deepseek-coder',
    name: 'DeepSeek Coder',
    description: 'Specialized model for coding tasks',
    contextLength: 16384,
    inputCost: 0.14,
    outputCost: 0.28,
    capabilities: ['text', 'code', 'programming'],
    provider: 'deepseek',
    isAvailable: true,
    lastUpdated: new Date()
  },
  'deepseek-reasoner': {
    id: 'deepseek-reasoner',
    name: 'DeepSeek Reasoner',
    description: 'Advanced reasoning model with chain-of-thought',
    contextLength: 65536,
    inputCost: 0.55,
    outputCost: 2.19,
    capabilities: ['text', 'reasoning', 'analysis', 'complex-reasoning'],
    provider: 'deepseek',
    isAvailable: true,
    lastUpdated: new Date()
  }
};

export class DeepSeekAdapter implements ProviderAdapter {
  readonly providerId = 'deepseek';
  readonly name = 'DeepSeek';
  readonly baseUrl = 'https://api.deepseek.com/v1';
  readonly authType = 'api-key' as const;

  async authenticate(config: AuthConfig): Promise<boolean> {
    if (!config.apiKey) return false;
    
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      
      return response.ok;
    } catch (error) {
      console.error('DeepSeek authentication failed:', error);
      return false;
    }
  }

  async discoverModels(_config: AuthConfig): Promise<RawModelInfo[]> {
    // Return static list for now
    return Object.keys(DEEPSEEK_MODELS).map(id => ({
      id,
      name: DEEPSEEK_MODELS[id]?.name,
      object: 'model',
      created: Date.now(),
      owned_by: 'deepseek',
    }));
  }

  async checkHealth(config?: AuthConfig): Promise<HealthStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'GET',
        headers: config?.apiKey ? {
          'Authorization': `Bearer ${config.apiKey}`,
        } : {},
        signal: AbortSignal.timeout(5000)
      });

      return {
        status: response.ok ? 'healthy' : 'down',
        latency: 0,
        lastCheck: new Date(),
        errorRate: 0,
        uptime: 100,
      };
    } catch (error) {
      return {
        status: 'down',
        latency: 0,
        lastCheck: new Date(),
        errorRate: 100,
        uptime: 0,
        details: {
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  normalizeModel(raw: RawModelInfo): ModelInfo {
    const staticModel = DEEPSEEK_MODELS[raw.id];
    if (staticModel) {
      return staticModel;
    }

    // Fallback for unknown models
    return {
      id: raw.id,
      name: raw.name || raw.id,
      description: `DeepSeek ${raw.name || raw.id}`,
      contextLength: 32768,
      inputCost: 0.14,
      outputCost: 0.28,
      capabilities: ['text', 'code'],
      provider: this.providerId,
      isAvailable: true,
      lastUpdated: new Date(),
    };
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    if (!apiKey.startsWith('sk-')) return false;
    
    try {
      return await this.authenticate({ type: 'api-key', apiKey });
    } catch {
      return false;
    }
  }
}
