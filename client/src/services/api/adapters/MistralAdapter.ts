/**
 * Mistral AI Provider Adapter for SahAI V2
 * Implements Mistral API integration
 */

import { 
  ProviderAdapter, 
  AuthConfig, 
  RawModelInfo, 
  ModelInfo, 
  HealthStatus 
} from '../../../types/api';

// Static model list for Mistral (following <PERSON><PERSON>'s pattern)
const MISTRAL_MODELS: Record<string, ModelInfo> = {
  'mistral-large-latest': {
    id: 'mistral-large-latest',
    name: 'Mistral Large',
    description: 'Mistral\'s most capable model for complex tasks',
    contextLength: 128000,
    inputCost: 2.00,
    outputCost: 6.00,
    capabilities: ['text', 'reasoning', 'code', 'tools'],
    provider: 'mistral',
    isAvailable: true,
    lastUpdated: new Date()
  },
  'mistral-medium-latest': {
    id: 'mistral-medium-latest',
    name: 'Mistral Medium',
    description: 'Balanced model for most use cases',
    contextLength: 32000,
    inputCost: 2.70,
    outputCost: 8.10,
    capabilities: ['text', 'reasoning', 'code'],
    provider: 'mistral',
    isAvailable: true,
    lastUpdated: new Date()
  },
  'mistral-small-latest': {
    id: 'mistral-small-latest',
    name: 'Mistral Small',
    description: 'Fast and efficient model for simple tasks',
    contextLength: 32000,
    inputCost: 0.20,
    outputCost: 0.60,
    capabilities: ['text', 'code'],
    provider: 'mistral',
    isAvailable: true,
    lastUpdated: new Date()
  },
  'codestral-latest': {
    id: 'codestral-latest',
    name: 'Codestral',
    description: 'Specialized model for coding tasks',
    contextLength: 32000,
    inputCost: 0.20,
    outputCost: 0.60,
    capabilities: ['text', 'code', 'programming'],
    provider: 'mistral',
    isAvailable: true,
    lastUpdated: new Date()
  }
};

export class MistralAdapter implements ProviderAdapter {
  readonly providerId = 'mistral';
  readonly name = 'Mistral AI';
  readonly baseUrl = 'https://api.mistral.ai/v1';
  readonly authType = 'api-key' as const;

  async authenticate(config: AuthConfig): Promise<boolean> {
    if (!config.apiKey) return false;
    
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      
      return response.ok;
    } catch (error) {
      console.error('Mistral authentication failed:', error);
      return false;
    }
  }

  async discoverModels(_config: AuthConfig): Promise<RawModelInfo[]> {
    // Return static list for now, but could fetch from API
    return Object.keys(MISTRAL_MODELS).map(id => ({
      id,
      name: MISTRAL_MODELS[id]?.name,
      object: 'model',
      created: Date.now(),
      owned_by: 'mistralai',
    }));
  }

  async checkHealth(config?: AuthConfig): Promise<HealthStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'GET',
        headers: config?.apiKey ? {
          'Authorization': `Bearer ${config.apiKey}`,
        } : {},
        signal: AbortSignal.timeout(5000)
      });

      return {
        status: response.ok ? 'healthy' : 'down',
        latency: 0,
        lastCheck: new Date(),
        errorRate: 0,
        uptime: 100,
      };
    } catch (error) {
      return {
        status: 'down',
        latency: 0,
        lastCheck: new Date(),
        errorRate: 100,
        uptime: 0,
        details: {
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  normalizeModel(raw: RawModelInfo): ModelInfo {
    const staticModel = MISTRAL_MODELS[raw.id];
    if (staticModel) {
      return staticModel;
    }

    // Fallback for unknown models
    return {
      id: raw.id,
      name: raw.name || raw.id,
      description: `Mistral ${raw.name || raw.id}`,
      contextLength: 32000,
      inputCost: 2.00,
      outputCost: 6.00,
      capabilities: ['text', 'code'],
      provider: this.providerId,
      isAvailable: true,
      lastUpdated: new Date(),
    };
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    // Mistral API keys don't have a specific prefix, just check length
    if (!apiKey || apiKey.length < 20) return false;
    
    try {
      return await this.authenticate({ type: 'api-key', apiKey });
    } catch {
      return false;
    }
  }
}
