/**
 * Code execution utilities for the message composer
 * Handles running code blocks in terminal or appropriate environments
 */

export interface CodeExecutionResult {
  success: boolean;
  output?: string;
  error?: string;
  executionTime?: number;
}

export interface CodeExecutionOptions {
  language: string;
  code: string;
  filename?: string;
  workingDirectory?: string;
}

/**
 * Execute code based on language type
 */
export async function executeCode(options: CodeExecutionOptions): Promise<CodeExecutionResult> {
  const { language, code, filename } = options;
  
  try {
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'js':
        return await executeJavaScript(code);
      
      case 'typescript':
      case 'ts':
        return await executeTypeScript(code);
      
      case 'python':
      case 'py':
        return await executePython(code, filename);
      
      case 'bash':
      case 'shell':
      case 'sh':
        return await executeBash(code);
      
      case 'powershell':
      case 'ps1':
        return await executePowerShell(code);
      
      default:
        return {
          success: false,
          error: `Execution not supported for language: ${language}`
        };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown execution error'
    };
  }
}

/**
 * Execute JavaScript code in a safe context
 */
async function executeJavaScript(code: string): Promise<CodeExecutionResult> {
  const startTime = Date.now();
  
  try {
    // Create a safe execution context
    const result = new Function(`
      const console = {
        log: (...args) => args.join(' '),
        error: (...args) => args.join(' '),
        warn: (...args) => args.join(' '),
        info: (...args) => args.join(' ')
      };
      
      ${code}
    `)();
    
    return {
      success: true,
      output: String(result || ''),
      executionTime: Date.now() - startTime
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'JavaScript execution error',
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Execute TypeScript code (compile to JS first)
 */
async function executeTypeScript(code: string): Promise<CodeExecutionResult> {
  // For now, treat as JavaScript
  // In a full implementation, you'd compile TS to JS first
  return executeJavaScript(code);
}

/**
 * Execute Python code via CEP ExtendScript bridge
 */
async function executePython(code: string, filename?: string): Promise<CodeExecutionResult> {
  const startTime = Date.now();

  try {
    // Check if we're in CEP environment
    if (typeof window !== 'undefined' && (window as any).CSInterface) {
      const csInterface = new (window as any).CSInterface();

      // Sanitize filename and code for ExtendScript
      const safeFilename = (filename || 'temp.py').replace(/[^a-zA-Z0-9._-]/g, '_');
      const safeCode = code
        .replace(/\\/g, '\\\\')  // Escape backslashes
        .replace(/"/g, '\\"')    // Escape quotes
        .replace(/\r?\n/g, '\\n'); // Normalize line endings

      // Create ExtendScript to execute Python with better error handling
      const extendScript = `
        try {
          var tempFile = new File(Folder.temp + "/${safeFilename}");

          if (!tempFile.open("w")) {
            throw new Error("Failed to create temporary file: " + tempFile.error);
          }

          tempFile.write("${safeCode}");
          tempFile.close();

          var result = system.callSystem("python \\"" + tempFile.fsName + "\\"");

          // Clean up
          if (tempFile.exists) {
            tempFile.remove();
          }

          // Return structured result
          JSON.stringify({
            success: true,
            output: result || "",
            exitCode: 0
          });

        } catch (e) {
          // Clean up on error
          try {
            if (tempFile && tempFile.exists) {
              tempFile.remove();
            }
          } catch (cleanupError) {
            // Ignore cleanup errors
          }

          JSON.stringify({
            success: false,
            error: "ExtendScript error: " + e.toString(),
            exitCode: -1
          });
        }
      `;

      return new Promise((resolve) => {
        csInterface.evalScript(extendScript, (result: any) => {
          try {
            // Handle enhanced CSInterface response format
            let parsedResult;
            if (result && typeof result === 'object' && result.success !== undefined) {
              // New enhanced format
              if (!result.success) {
                resolve({
                  success: false,
                  error: result.error || 'Unknown CSInterface error',
                  executionTime: Date.now() - startTime
                });
                return;
              }
              parsedResult = JSON.parse(result.result);
            } else {
              // Legacy format - try to parse as JSON
              parsedResult = JSON.parse(result);
            }

            resolve({
              success: parsedResult.success,
              output: parsedResult.output,
              error: parsedResult.error,
              executionTime: Date.now() - startTime
            });
          } catch (parseError) {
            // Fallback for non-JSON responses
            resolve({
              success: false,
              error: `Failed to parse execution result: ${parseError}. Raw result: ${result}`,
              executionTime: Date.now() - startTime
            });
          }
        });
      });
    } else {
      return {
        success: false,
        error: 'Python execution requires CEP environment',
        executionTime: Date.now() - startTime
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Python execution error',
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Execute Bash commands via CEP ExtendScript bridge
 */
async function executeBash(code: string): Promise<CodeExecutionResult> {
  const startTime = Date.now();

  try {
    if (typeof window !== 'undefined' && (window as any).CSInterface) {
      const csInterface = new (window as any).CSInterface();

      // Sanitize command for ExtendScript
      const safeCommand = code
        .replace(/\\/g, '\\\\')  // Escape backslashes
        .replace(/"/g, '\\"')    // Escape quotes
        .replace(/\r?\n/g, ' && '); // Chain commands with &&

      const extendScript = `
        try {
          var result = system.callSystem("${safeCommand}");
          JSON.stringify({
            success: true,
            output: result || "",
            exitCode: 0
          });
        } catch (e) {
          JSON.stringify({
            success: false,
            error: "Command execution error: " + e.toString(),
            exitCode: -1
          });
        }
      `;

      return new Promise((resolve) => {
        csInterface.evalScript(extendScript, (result: any) => {
          try {
            // Handle enhanced CSInterface response format
            let parsedResult;
            if (result && typeof result === 'object' && result.success !== undefined) {
              // New enhanced format
              if (!result.success) {
                resolve({
                  success: false,
                  error: result.error || 'Unknown CSInterface error',
                  executionTime: Date.now() - startTime
                });
                return;
              }
              parsedResult = JSON.parse(result.result);
            } else {
              // Legacy format - try to parse as JSON
              parsedResult = JSON.parse(result);
            }

            resolve({
              success: parsedResult.success,
              output: parsedResult.output,
              error: parsedResult.error,
              executionTime: Date.now() - startTime
            });
          } catch (parseError) {
            // Fallback for non-JSON responses
            resolve({
              success: false,
              error: `Failed to parse execution result: ${parseError}. Raw result: ${result}`,
              executionTime: Date.now() - startTime
            });
          }
        });
      });
    } else {
      return {
        success: false,
        error: 'Bash execution requires CEP environment',
        executionTime: Date.now() - startTime
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Bash execution error',
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Execute PowerShell commands via CEP ExtendScript bridge
 */
async function executePowerShell(code: string): Promise<CodeExecutionResult> {
  const startTime = Date.now();

  try {
    if (typeof window !== 'undefined' && (window as any).CSInterface) {
      const csInterface = new (window as any).CSInterface();

      // Sanitize PowerShell command for ExtendScript
      const safeCommand = code
        .replace(/\\/g, '\\\\')  // Escape backslashes
        .replace(/"/g, '\\"')    // Escape quotes
        .replace(/\r?\n/g, '; '); // Chain commands with semicolons

      const extendScript = `
        try {
          // Create a temporary PowerShell script file for better execution
          var tempFile = new File(Folder.temp + "/sahai_ps_script.ps1");

          if (!tempFile.open("w")) {
            throw new Error("Failed to create temporary PowerShell script: " + tempFile.error);
          }

          tempFile.write("${safeCommand}");
          tempFile.close();

          // Execute with -ExecutionPolicy Bypass for better compatibility
          var result = system.callSystem('powershell -ExecutionPolicy Bypass -File "' + tempFile.fsName + '"');

          // Clean up
          if (tempFile.exists) {
            tempFile.remove();
          }

          JSON.stringify({
            success: true,
            output: result || "",
            exitCode: 0
          });
        } catch (e) {
          // Clean up on error
          try {
            if (tempFile && tempFile.exists) {
              tempFile.remove();
            }
          } catch (cleanupError) {
            // Ignore cleanup errors
          }

          JSON.stringify({
            success: false,
            error: "PowerShell execution error: " + e.toString(),
            exitCode: -1
          });
        }
      `;

      return new Promise((resolve) => {
        csInterface.evalScript(extendScript, (result: any) => {
          try {
            // Handle enhanced CSInterface response format
            let parsedResult;
            if (result && typeof result === 'object' && result.success !== undefined) {
              // New enhanced format
              if (!result.success) {
                resolve({
                  success: false,
                  error: result.error || 'Unknown CSInterface error',
                  executionTime: Date.now() - startTime
                });
                return;
              }
              parsedResult = JSON.parse(result.result);
            } else {
              // Legacy format - try to parse as JSON
              parsedResult = JSON.parse(result);
            }

            resolve({
              success: parsedResult.success,
              output: parsedResult.output,
              error: parsedResult.error,
              executionTime: Date.now() - startTime
            });
          } catch (parseError) {
            // Fallback for non-JSON responses
            resolve({
              success: false,
              error: `Failed to parse execution result: ${parseError}. Raw result: ${result}`,
              executionTime: Date.now() - startTime
            });
          }
        });
      });
    } else {
      return {
        success: false,
        error: 'PowerShell execution requires CEP environment',
        executionTime: Date.now() - startTime
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'PowerShell execution error',
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Get supported languages for code execution
 */
export function getSupportedLanguages(): string[] {
  return [
    'javascript',
    'js',
    'typescript',
    'ts',
    'python',
    'py',
    'bash',
    'shell',
    'sh',
    'powershell',
    'ps1'
  ];
}

/**
 * Check if a language is supported for execution
 */
export function isLanguageSupported(language: string): boolean {
  return getSupportedLanguages().includes(language.toLowerCase());
}

/**
 * Format execution result for display
 */
export function formatExecutionResult(result: CodeExecutionResult): string {
  if (result.success) {
    let output = `✅ Execution completed`;
    if (result.executionTime) {
      output += ` in ${result.executionTime}ms`;
    }
    if (result.output) {
      output += `\n\nOutput:\n${result.output}`;
    }
    return output;
  } else {
    let output = `❌ Execution failed`;
    if (result.executionTime) {
      output += ` after ${result.executionTime}ms`;
    }
    if (result.error) {
      output += `\n\nError:\n${result.error}`;
    }
    return output;
  }
}
