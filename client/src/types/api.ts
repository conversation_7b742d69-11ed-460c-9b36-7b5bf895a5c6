/**
 * API Types for SahAI V2 Provider System
 * Comprehensive type definitions for API service layer
 */

// Core API interfaces
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface ApiErrorData {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
  timestamp: Date;
}

export class ApiError extends Error implements ApiErrorData {
  code: string;
  details?: any;
  retryable: boolean;
  timestamp: Date;

  constructor(data: ApiErrorData) {
    super(data.message);
    this.name = 'ApiError';
    this.code = data.code;
    this.details = data.details;
    this.retryable = data.retryable;
    this.timestamp = data.timestamp;
  }
}

// Request configuration
export interface RequestConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  providerId: string;
}

// Authentication types
export interface AuthConfig {
  type: 'bearer' | 'api-key' | 'basic' | 'custom' | 'oauth';
  token?: string;
  apiKey?: string;
  username?: string;
  password?: string;
  customHeaders?: Record<string, string>;
}

// Model information from APIs
export interface RawModelInfo {
  id: string;
  name?: string;
  description?: string;
  created?: number;
  owned_by?: string;
  object?: string;
  [key: string]: any; // Allow for provider-specific fields
}

export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  inputCost?: number;
  outputCost?: number;
  capabilities?: string[];
  provider: string;
  isAvailable: boolean;
  lastUpdated: Date;
  metadata?: Record<string, any>;
}

// Health status
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'down';
  latency: number;
  lastCheck: Date;
  errorRate: number;
  uptime: number;
  details?: {
    endpoint?: string;
    responseTime?: number;
    errorMessage?: string;
    consecutiveFailures?: number;
  };
}

// Provider adapter interface
export interface ProviderAdapter {
  readonly providerId: string;
  readonly name: string;
  readonly baseUrl: string;
  readonly authType: AuthConfig['type'];
  
  // Core methods
  authenticate(config: AuthConfig): Promise<boolean>;
  discoverModels(config: AuthConfig): Promise<RawModelInfo[]>;
  checkHealth(config?: AuthConfig): Promise<HealthStatus>;
  normalizeModel(raw: RawModelInfo): ModelInfo;
  
  // Optional methods
  validateApiKey?(apiKey: string): Promise<boolean>;
  getModelDetails?(modelId: string, config: AuthConfig): Promise<RawModelInfo>;
  getRateLimits?(config: AuthConfig): Promise<RateLimitInfo>;
}

// Rate limiting - Provider-specific rate limit information
// Note: Currently used as optional interface for future provider implementations
// The API service uses RateLimitState for internal rate limiting
export interface RateLimitInfo {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  tokensPerMinute?: number;
  tokensPerHour?: number;
  tokensPerDay?: number;
  resetTime?: Date;
}

export interface RateLimitState {
  requests: number;
  tokens: number;
  resetTime: Date;
  isLimited: boolean;
}

// Circuit breaker
export interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failureCount: number;
  lastFailureTime?: Date;
  nextAttemptTime?: Date;
  successCount: number;
}

// Provider-specific configurations
export interface OpenAIConfig extends AuthConfig {
  organization?: string;
  project?: string;
}

export interface AnthropicConfig extends AuthConfig {
  version?: string;
}

export interface GroqConfig extends AuthConfig {
  // Groq uses OpenAI-compatible API
}

export interface OllamaConfig {
  baseUrl: string;
  timeout?: number;
}

export interface OpenRouterConfig extends AuthConfig {
  httpReferer?: string;
  xTitle?: string;
}

// API service configuration
export interface ApiServiceConfig {
  timeout: number;
  retries: number;
  rateLimitEnabled: boolean;
  circuitBreakerEnabled: boolean;
  cacheEnabled: boolean;
  healthCheckInterval: number;
}

// Batch operations
export interface BatchRequest {
  id: string;
  config: RequestConfig;
}

export interface BatchResponse<T = any> {
  id: string;
  success: boolean;
  data?: T;
  error?: ApiError;
}

// Webhook/streaming support
export interface StreamConfig {
  onData: (chunk: any) => void;
  onError: (error: ApiError) => void;
  onComplete: () => void;
}

// Provider discovery result
export interface ProviderDiscoveryResult {
  providerId: string;
  models: ModelInfo[];
  health: HealthStatus;
  lastUpdated: Date;
  error?: ApiError;
}
