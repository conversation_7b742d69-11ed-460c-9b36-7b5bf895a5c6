// src/components/modals/ProviderHealthModal.tsx
import React, { useState, useEffect } from 'react';
import clsx from 'clsx';
import { useSettingsStore } from '../../stores/settingsStore';
import { <PERSON>dalHeader } from './ModalHeader';
import { RefreshIcon, Card, Tooltip } from '../ui';
import styles from './ProviderHealth.module.css';

interface Props {
  onClose: () => void;
  onBack?: () => void;
}

interface HealthData {
  cpu: string;
  memory: string;
  latency: string;
  status: 'Online' | 'Degraded' | 'Offline';
  statusClass: 'good' | 'warning' | 'critical';
}

export const ProviderHealthModal: React.FC<Props> = ({ onClose, onBack }) => {
  const { currentProvider } = useSettingsStore();
  const [data, setData] = useState<HealthData | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const fetchHealth = async () => {
    setIsChecking(true);
    await new Promise(r => setTimeout(r, 600)); // simulate API
    setData({
      cpu: (Math.random() * 80 + 10).toFixed(1),
      memory: (Math.random() * 7 + 1).toFixed(2),
      latency: (Math.random() * 150 + 30).toFixed(0),
      status: Math.random() < 0.8 ? 'Online' : Math.random() < 0.95 ? 'Degraded' : 'Offline',
      statusClass: Math.random() < 0.8 ? 'good' : Math.random() < 0.95 ? 'warning' : 'critical',
    });
    setIsChecking(false);
  };

  useEffect(() => {
    fetchHealth();
  }, [currentProvider]);

  const colorMap = { good: '#00B341', warning: '#FFB900', critical: '#FF453A' };

  const getStatusTooltip = (statusClass: string) => {
    switch (statusClass) {
      case 'good': return 'All systems operating normally';
      case 'warning': return 'Some performance degradation detected';
      case 'critical': return 'Service is offline or experiencing issues';
      default: return 'Status unknown';
    }
  };

  return (
    <div className={styles.wrapper}>
      <ModalHeader title="Provider Health" onClose={onClose} {...(onBack && { onBack, showBackButton: true })}>
        <button className={styles.refresh} onClick={fetchHealth} disabled={isChecking}>
          <RefreshIcon size={16} className={isChecking ? styles.spin : undefined} />
        </button>
      </ModalHeader>

      <div className={styles.body}>
        {/* Provider name */}
        <div className={styles.modelName}>{currentProvider?.name || 'No provider selected'}</div>

        {/* Status Legend */}
        <div className={styles.legend}>
          <span className={styles.legendTitle}>Status Legend:</span>
          <div className={styles.legendItems}>
            <span className={styles.legendItem}>
              <span className={clsx(styles.dot, styles.good)}></span>
              Good
            </span>
            <span className={styles.legendItem}>
              <span className={clsx(styles.dot, styles.warning)}></span>
              Warning
            </span>
            <span className={styles.legendItem}>
              <span className={clsx(styles.dot, styles.critical)}></span>
              Offline
            </span>
          </div>
        </div>

        {/* 4-widget grid */}
        <div className={styles.grid}>
          {[
            {
              label: 'CPU Usage',
              value: `${data?.cpu ?? '--'}%`,
              progress: data ? +data.cpu : undefined,
              progressColor: colorMap[data?.statusClass ?? 'good'],
              tooltip: 'Current CPU utilization of the provider service'
            },
            {
              label: 'Memory',
              value: `${data?.memory ?? '--'} GB`,
              progress: data ? (+data.memory / 8) * 100 : undefined,
              progressColor: colorMap[data?.statusClass ?? 'good'],
              tooltip: 'Memory usage out of 8GB total capacity'
            },
            {
              label: 'Latency',
              value: `${data?.latency ?? '--'} ms`,
              progress: data ? (+data.latency / 200) * 100 : undefined,
              progressColor: colorMap[data?.statusClass ?? 'good'],
              tooltip: 'Average response time for API requests'
            },
            {
              label: 'Status',
              value: data?.status ?? '--',
              tooltip: getStatusTooltip(data?.statusClass ?? 'good')
            },
          ].map(({ label, value, progress, progressColor, tooltip }) => (
            <Tooltip key={label} content={tooltip} position="top">
              <Card
                label={label}
                value={value}
                {...(progress !== undefined && { progress })}
                {...(progressColor && { progressColor })}
                variant="status"
                className={styles.healthCard || ''}
                isLoading={isChecking}
              />
            </Tooltip>
          ))}
        </div>

        {/* mini log */}
        <div className={styles.log}>
          <span className={styles.logTitle}>System Log</span>
          <p className={styles.logLine}>
            {data ? `[${new Date().toLocaleTimeString()}] Health data fetched.` : 'Fetching…'}
          </p>
        </div>
      </div>
    </div>
  );
};