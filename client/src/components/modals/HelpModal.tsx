/**
 * Adobe CEP Help Modal
 * One-file, no overrides, ready to drop in
 */

import React from 'react';
import { ModalHeader } from './ModalHeader';
import { FAQItem } from '../ui';
import styles from './Help.module.css';

interface Props {
  onClose: () => void;
  onBack?: () => void;
}

export const HelpModal: React.FC<Props> = ({ onClose, onBack }) => {

  const faq = [
    {
      id: 'api-keys',
      question: 'How do I set up API keys?',
      answer: 'Go to Settings → Advanced Config → enter your API keys. All keys are encrypted and stored locally for security.',
    },
    {
      id: 'supported-apps',
      question: 'Which Adobe apps are supported?',
      answer: 'SahAI Extension supports After Effects, Premiere Pro, Photoshop & Illustrator 2025 and newer versions.',
    },
    {
      id: 'model-comparison',
      question: 'How to compare models?',
      answer: 'Use the Model Comparison feature in settings to view side-by-side performance metrics and choose the best model for your needs.',
    },
    {
      id: 'multi-model',
      question: 'Multi-model chat?',
      answer: 'Enable Multi-Model Chat in settings to query several AI models simultaneously and compare their responses.',
    },
    {
      id: 'troubleshooting',
      question: 'Troubleshooting connection issues?',
      answer: 'Check your API keys, network connection, and provider status. View detailed logs in the Analytics section for debugging.',
    },
    {
      id: 'data-security',
      question: 'Is my data secure?',
      answer: 'Yes! API keys are encrypted locally, conversations are ephemeral, and no data is sent to our servers.',
    },
  ];



  return (
    <div className={styles.wrapper}>
      <ModalHeader
        title="Help & Support"
        onClose={onClose}
        {...(onBack && { onBack, showBackButton: true })}
      />

      <main className={styles.body}>
        <h2 className={styles.title}>Quick Start</h2>
        <ol className={styles.steps}>
          <li>Configure provider key.</li>
          <li>Select model.</li>
          <li>Start chatting.</li>
          <li>Use Analytics & compare models.</li>
        </ol>

        <h2 className={styles.title}>FAQ</h2>
        <div className={styles.faqList}>
          {faq.map((item) => (
            <FAQItem
              key={item.id}
              question={item.question}
              answer={item.answer}
              questionId={item.id}
              className={styles.faqItem || ''}
            />
          ))}
        </div>

        <h2 className={styles.title}>Need More Help?</h2>
        <div className={styles.links}>
          <button>📖 Docs</button>
          <button>💬 Community</button>
          <button>🐛 Report Bug</button>
          <button>✉️ Support</button>
        </div>

        <h2 className={styles.title}>Pro Tips</h2>
        <ul className={styles.tips}>
          <li><strong>Ctrl+Enter</strong> → send</li>
          <li>Hover for tooltips</li>
          <li>Try different models</li>
          <li>Save key chats</li>
        </ul>
      </main>
    </div>
  );
};