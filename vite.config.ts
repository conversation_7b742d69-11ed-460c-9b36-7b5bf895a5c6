import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
// @ts-ignore
import cspHashPlugin from './build-tools/vite-plugin-csp-hash.js';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    cspHashPlugin({
      enabled: true,
      logLevel: 'info'
    }),

    // Fix HTML for CEP compatibility
    {
      name: 'fix-html-for-cep',
      transformIndexHtml(html) {
        // Remove type="module" from script tags for CEP compatibility
        return html.replace(/type="module"\s+crossorigin/g, 'crossorigin');
      },
    },
  ],
  
  // Build configuration optimized for CEP
  build: {
    outDir: 'dist',
    emptyOutDir: true,

    // CEP extensions need relative paths
    assetsDir: './assets',

    rollupOptions: {
      input: resolve(__dirname, 'client/index.html'),
      output: {
        // CEP-compatible output format (IIFE instead of ES modules)
        format: 'iife',

        // Organize assets in subdirectories
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: (assetInfo: any) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];

          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return 'assets/images/[name]-[hash][extname]';
          }
          if (/css/i.test(ext)) {
            return 'assets/css/[name]-[hash][extname]';
          }
          if (/woff2?|eot|ttf|otf/i.test(ext)) {
            return 'assets/fonts/[name]-[hash][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        },

        // Global variable name for IIFE
        name: 'SahAIApp',
      },
    },

    // Optimize for CEP environment (older Chromium)
    target: 'es2017',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console for production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.warn', 'console.error'],
      },
      mangle: {
        toplevel: true,
      },
    },

    // Disable source maps for production to reduce size
    sourcemap: false,
    
    // Enable chunk splitting
    chunkSizeWarningLimit: 500,
  },
  
  // Development server configuration
  server: {
    port: 8080,
    host: 'localhost',
    open: false,
    cors: true,
    
    // Proxy configuration for local development
    proxy: {
      // Add proxy rules if needed for API calls during development
    },
  },
  
  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, 'client/src'),
      '@/components': resolve(__dirname, 'client/src/components'),
      '@/features': resolve(__dirname, 'client/src/features'),
      '@/hooks': resolve(__dirname, 'client/src/hooks'),
      '@/services': resolve(__dirname, 'client/src/services'),
      '@/stores': resolve(__dirname, 'client/src/stores'),
      '@/styles': resolve(__dirname, 'client/src/styles'),
      '@/types': resolve(__dirname, 'client/src/types'),
      '@/utils': resolve(__dirname, 'client/src/utils'),
      // Ensure CSInterface utils are properly resolved
      '@/cep': resolve(__dirname, 'client/src/utils/cep'),
    },
  },
  
  // CSS configuration
  css: {
    modules: {
      // CSS Modules configuration
      localsConvention: 'camelCaseOnly',
      generateScopedName: '[name]__[local]--[hash:base64:5]',
    },
    preprocessorOptions: {
      scss: {
        // SCSS preprocessor options (no global imports needed currently)
      },
    },
  },
  
  // Define global constants
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __PROD__: JSON.stringify(process.env.NODE_ENV === 'production'),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
  },
  
  // Optimization
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'zustand',
      'axios',
    ],
    exclude: [
      // Exclude any problematic dependencies
    ],
  },
  
  // Environment variables
  envPrefix: 'VITE_',
  
  // Base path for assets (important for CEP)
  base: './',
  
  // Preview server configuration (for production builds)
  preview: {
    port: 8081,
    host: 'localhost',
    open: false,
  },
  
  // Worker configuration for CEP compatibility
  worker: {
    format: 'iife',
  },
  
  // JSON configuration
  json: {
    namedExports: true,
    stringify: false,
  },
  
  // ESBuild configuration for CEP compatibility
  esbuild: {
    target: 'es2017',
    jsxFactory: 'React.createElement',
    jsxFragment: 'React.Fragment',
    // Ensure compatibility with older Chromium in CEP
    supported: {
      'dynamic-import': false,
      'import-meta': false,
    },
  },
});
